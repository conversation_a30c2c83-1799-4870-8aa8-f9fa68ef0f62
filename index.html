<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>课程学习助手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }

        #jsonpInput {
            flex: 1;
            height: 150px;
            margin-bottom: 10px;
        }

        #logOutput {
            width: 100%;
            height: 300px;
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }

        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .clear-button {
            padding: 5px 10px;
            background-color: #f8d7da;
            border: 1px solid #dc3545;
            color: #dc3545;
            cursor: pointer;
            border-radius: 4px;
        }

        .clear-button:hover {
            background-color: #dc3545;
            color: white;
        }

        .stop-button {
            background-color: #dc3545;
            color: white;
            border: 1px solid #dc3545;
        }

        .stop-button:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        /* 课程配置样式 */
        .course-config-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f9f9f9;
            transition: box-shadow 0.2s;
        }

        .course-config-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .course-config-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .course-config-info {
            color: #666;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .course-config-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }

        .course-config-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .course-config-loading {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="instructions">
            <h2>使用说明</h2>
            <p>由于浏览器的安全限制，直接打开本页面会遇到跨域问题。请选择以下任一浏览器，按步骤操作：</p>

            <h3>Chrome浏览器：</h3>
            <ol>
                <li>关闭所有Chrome浏览器窗口</li>
                <li>创建一个空文件夹(例如: chrome_temp)</li>
                <li>使用以下命令启动Chrome：
                    <br>
                    <strong>Windows:</strong>
                    <br>
                    <code>"C:\Program Files\Google\Chrome\Application\chrome.exe" --disable-web-security --user-data-dir="C:\chrome_temp"</code>
                    <br>
                    <strong>MacOS:</strong>
                    <br>
                    <code>open -n -a "Google Chrome" --args --disable-web-security --user-data-dir="/Users/<USER>/chrome_temp"</code>
                </li>
            </ol>

            <p><strong>注意事项：</strong></p>
            <ul>
                <li>请根据实际安装路径调整浏览器执行文件的位置</li>
                <li>临时文件夹路径可以自定义，但必须是一个空文件夹</li>
                <li>启动后会看到浏览器提示"您使用的是不受支持的命令行标记 --disable-web-security"，这是正常现象</li>
                <li>如果使用MacOS，请将[用户名]替换为实际的用户名</li>
                <li>不建议使用IE浏览器，因为：
                    <ul>
                        <li>IE浏览器对现代Web标准支持不完善</li>
                        <li>可能存在兼容性问题</li>
                        <li>性能相对较差</li>
                        <li>Microsoft已不再支持IE浏览器</li>
                    </ul>
                </li>
                <li>推荐使用Chrome或搜狗浏览器运行本程序</li>
            </ul>

            <h3>最后一步：</h3>
            <p>在配置好的浏览器中打开本页面即可正常使用</p>
        </div>
        <h1>课程学习助手</h1>
        <div style="display: flex; gap: 30px; align-items: flex-start; margin-bottom: 20px;">
            <div>
                <h3 style="margin-bottom: 10px;">请选择培训类型：</h3>
                <select id="trainType" style="padding: 5px;" onchange="loadCourseConfigs()">
                    <option value="teacher">教师暑假</option>
                    <option value="student">师范生免试</option>
                </select>
            </div>
            <div>
                <h3 style="margin-bottom: 10px;">批量设置学习数量：</h3>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="number" id="batchResourceCount" placeholder="留空学习全部" min="1"
                        style="padding: 5px; width: 120px;">
                    <button onclick="setBatchConfig()" style="padding: 5px 10px;">应用到全部</button>
                    <button onclick="resetAllConfigs()"
                        style="padding: 5px 10px; background-color: #f8d7da; border: 1px solid #dc3545; color: #dc3545;">重置</button>
                </div>
            </div>
        </div>

        <!-- 课程配置区域 -->
        <div id="courseConfigSection" style="margin-bottom: 20px;">
            <h3>课程学习配置：</h3>
            <div id="courseConfigContainer"
                style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 10px;">
                <!-- 课程配置项将在这里动态生成 -->
            </div>
        </div>
        <div>
            <h3>请输入JSONP响应数据:</h3>
            <div class="input-group">
                <textarea id="jsonpInput" placeholder="请输入JSONP响应数据..."></textarea>
                <button class="clear-button" onclick="clearJsonpInput()">清空输入</button>
            </div>
        </div>
        <button id="learningButton" onclick="toggleLearning()">开始学习</button>
        <div>
            <h3>执行日志:</h3>
            <div id="logOutput"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let macId = '';
        let macKey = '';
        let userId = '';
        const appId = 'e5649925-441d-4a53-b525-51a2f1c4e0a8';
        const TEACHER_TRAIN_ID = '10f7b3d6-e1c6-4a2e-ba76-e2f2af4674a5';
        const STUDENT_TRAIN_ID = '9b088c40-0824-43e0-b870-b94f617d4a37';
        const tags = '2025年暑期教师研修';
        const origin = '2025年暑期教师研修';

        // API基础URL常量
        const API_BASE_URLS = {
            TRAIN_API: 'https://s-file-1.ykt.cbern.com.cn/teach/api_static/trains/',
            COURSE_API: 'https://s-file-1.ykt.cbern.com.cn/teach/s_course/v2/',
            STUDY_RECORD_API: 'https://x-study-record-api.ykt.eduyun.cn/v1/',
            TRAIN_GATEWAY_API: 'https://elearning-train-gateway.ykt.eduyun.cn/v1/',
            TRAIN_GATEWAY_V2_API: 'https://elearning-train-gateway.ykt.eduyun.cn/v2/'
        };

        // 课程配置管理器
        const CourseConfigManager = {
            configs: new Map(), // 存储每个课程的学习数量配置

            // 设置课程学习数量
            setCourseConfig(courseId, count) {
                if (this.validateCount(count)) {
                    this.configs.set(courseId, count);
                    return true;
                }
                return false;
            },

            // 获取课程学习数量
            getCourseConfig(courseId) {
                return this.configs.get(courseId) || null;
            },

            // 批量设置所有课程
            setAllCourses(count) {
                if (this.validateCount(count)) {
                    for (const courseId of this.configs.keys()) {
                        this.configs.set(courseId, count);
                    }
                    this.updateAllInputs(count);
                    return true;
                }
                return false;
            },

            // 验证数量
            validateCount(count) {
                return count === null || (Number.isInteger(count) && count > 0);
            },

            // 更新所有输入框
            updateAllInputs(count) {
                const inputs = document.querySelectorAll('.course-config-input');
                inputs.forEach(input => {
                    input.value = count || '';
                });
            },

            // 重置所有配置
            resetAll() {
                this.configs.clear();
                this.updateAllInputs('');
            }
        };

        // 学习状态管理变量
        let isLearning = false;
        let shouldStop = false;

        // 获取当前选中的培训类型
        function getSelectedTrainType() {
            return document.getElementById('trainType').value;
        }

        // 获取当前trainId
        function getCurrentTrainId() {
            return getSelectedTrainType() === 'student' ? STUDENT_TRAIN_ID : TEACHER_TRAIN_ID;
        }

        // 获取指定课程的学习数量配置
        function getCourseResourceCount(courseId) {
            return CourseConfigManager.getCourseConfig(courseId);
        }

        // 验证资源数量输入
        function validateResourceCount(count) {
            if (count === null || count === '') {
                return true; // 空值表示学习全部，这是有效的
            }
            const num = parseInt(count, 10);
            if (isNaN(num) || num < 1 || !Number.isInteger(num)) {
                return false;
            }
            return true;
        }

        // 更新课程配置
        function updateCourseConfig(courseId, value) {
            const input = document.querySelector(`[data-course-id="${courseId}"]`);
            const count = value.trim() === '' ? null : parseInt(value, 10);

            // 获取课程信息用于验证
            const maxResources = input ? parseInt(input.getAttribute('max')) : 999;

            if (validateResourceCount(count)) {
                // 检查是否超过最大资源数
                if (count !== null && count > maxResources) {
                    log(`警告: 课程学习数量 ${count} 超过了该课程的总资源数 ${maxResources}，已调整为 ${maxResources}`);
                    const adjustedCount = maxResources;
                    CourseConfigManager.setCourseConfig(courseId, adjustedCount);
                    if (input) input.value = adjustedCount;
                } else {
                    CourseConfigManager.setCourseConfig(courseId, count);
                    log(`课程配置已更新: ${getCourseName(courseId)} - 学习数量: ${count || '全部'}`);
                }
            } else {
                log(`警告: 学习数量设置无效，必须是正整数或留空`);
                // 重置输入框
                if (input) {
                    input.value = '';
                    CourseConfigManager.setCourseConfig(courseId, null);
                }
            }
        }

        // 获取课程名称（用于日志显示）
        function getCourseName(courseId) {
            const configItem = document.querySelector(`[data-course-id="${courseId}"]`)?.closest('.course-config-item');
            if (configItem) {
                const header = configItem.querySelector('.course-config-header');
                return header ? header.textContent : courseId;
            }
            return courseId;
        }

        // 批量设置配置
        function setBatchConfig() {
            const input = document.getElementById('batchResourceCount');
            const value = input.value.trim();
            const count = value === '' ? null : parseInt(value, 10);

            if (validateResourceCount(count)) {
                // 获取所有课程输入框并应用设置
                const courseInputs = document.querySelectorAll('.course-config-input');
                let updatedCount = 0;

                courseInputs.forEach(courseInput => {
                    const courseId = courseInput.getAttribute('data-course-id');
                    const maxResources = parseInt(courseInput.getAttribute('max'));

                    if (count !== null && count > maxResources) {
                        // 如果设置的数量超过该课程的最大资源数，使用最大资源数
                        courseInput.value = maxResources;
                        CourseConfigManager.setCourseConfig(courseId, maxResources);
                    } else {
                        courseInput.value = count || '';
                        CourseConfigManager.setCourseConfig(courseId, count);
                    }
                    updatedCount++;
                });

                log(`批量设置完成: ${updatedCount}个课程的学习数量已设置为 ${count || '全部'}`);
                if (count !== null) {
                    log('注意: 部分课程的设置可能已调整为该课程的最大资源数');
                }
            } else {
                log('警告: 批量设置的学习数量无效，必须是正整数或留空');
            }
        }

        // 重置所有配置
        function resetAllConfigs() {
            CourseConfigManager.resetAll();
            document.getElementById('batchResourceCount').value = '';
            log('已重置所有课程配置');
        }

        // 加载课程配置（当培训类型改变时调用）
        async function loadCourseConfigs() {
            const container = document.getElementById('courseConfigContainer');
            if (container) {
                container.innerHTML = '<div class="course-config-loading">正在加载课程列表...</div>';
            }

            // 重置配置管理器
            CourseConfigManager.resetAll();

            try {
                // 重新获取课程列表并生成UI
                await loadCourseListForConfig();
            } catch (error) {
                if (container) {
                    container.innerHTML = '<div class="course-config-loading">加载课程列表失败，请检查网络连接</div>';
                }
                log(`加载课程配置失败: ${error.message}`);
            }
        }

        // 专门用于加载课程配置的函数（不需要认证）
        async function loadCourseListForConfig() {
            const trainType = getSelectedTrainType();
            const currentTrainId = getCurrentTrainId();
            const url = trainType === 'student'
                ? `${API_BASE_URLS.TRAIN_API}${currentTrainId}/train_courses.json`
                : `${API_BASE_URLS.TRAIN_API}2025sqpx/train_courses.json`;

            log(`正在加载课程列表: ${url}`);

            try {
                // 由于跨域限制，先尝试使用JSONP方式，如果失败则显示预设课程
                const response = await fetch(url, {
                    mode: 'cors',
                    credentials: 'omit'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const courseArray = Array.isArray(data) ? data : data.data || [];

                // 处理课程数据
                const processedData = courseArray.map(course => ({
                    courseId: course.course_id,
                    courseName: course.title,
                    resourceTotalCount: parseInt(course.resource_total_count) || 0,
                    frontCoverUrl: course.front_cover_url
                })).filter(course => course.courseId);

                if (processedData.length === 0) {
                    throw new Error('没有找到有效的课程');
                }

                // 生成课程配置UI
                generateCourseConfigUI(processedData);
                log(`已加载${processedData.length}个课程的配置界面`);

                return processedData;

            } catch (error) {
                log(`无法直接获取课程列表 (${error.message})，使用预设课程列表`);
                // 使用预设的9个课程
                const presetCourses = getPresetCourses(trainType);
                generateCourseConfigUI(presetCourses);
                log(`已加载${presetCourses.length}个预设课程的配置界面`);
                return presetCourses;
            }
        }

        // 获取预设课程列表（基于网络分析的结果）
        function getPresetCourses(trainType) {
            if (trainType === 'student') {
                // 师范生免试课程（需要根据实际情况调整）
                return [
                    { courseId: 'student_course_1', courseName: '师范生专业课程1', resourceTotalCount: 50 },
                    { courseId: 'student_course_2', courseName: '师范生专业课程2', resourceTotalCount: 45 },
                    { courseId: 'student_course_3', courseName: '师范生专业课程3', resourceTotalCount: 40 },
                    { courseId: 'student_course_4', courseName: '师范生专业课程4', resourceTotalCount: 35 },
                    { courseId: 'student_course_5', courseName: '师范生专业课程5', resourceTotalCount: 30 },
                    { courseId: 'student_course_6', courseName: '师范生专业课程6', resourceTotalCount: 25 },
                    { courseId: 'student_course_7', courseName: '师范生专业课程7', resourceTotalCount: 20 },
                    { courseId: 'student_course_8', courseName: '师范生专业课程8', resourceTotalCount: 15 },
                    { courseId: 'student_course_9', courseName: '师范生专业课程9', resourceTotalCount: 10 }
                ];
            } else {
                // 教师暑假培训课程（基于网络分析的实际课程）
                return [
                    { courseId: '895caa6f-6c42-411d-ab7c-2b43facebd9f', courseName: '大力弘扬教育家精神', resourceTotalCount: 193 },
                    { courseId: '2a4f51b9-1086-4296-92d9-c4cf8ff00538', courseName: '数字素养提升', resourceTotalCount: 315 },
                    { courseId: 'e3b6492d-bc7c-4440-ab5e-8d02debd8ceb', courseName: '科学素养提升', resourceTotalCount: 74 },
                    { courseId: '1034859d-512f-4696-999d-e736456a75af', courseName: '心理健康教育能力提升', resourceTotalCount: 12 },
                    { courseId: 'c5d0f0a7-9047-496e-bb03-e37ea5e65dd7', courseName: '学前教育专题培训', resourceTotalCount: 96 },
                    { courseId: 'cb134d8b-ebe5-4953-8c2c-10d27b45b8dc', courseName: '2022年版新课标国家级示范培训-案例专题', resourceTotalCount: 12 },
                    { courseId: '0bc83fd8-4ee9-4bb2-bf9d-f858ee13ed8f', courseName: '实验室安全管理', resourceTotalCount: 22 },
                    { courseId: 'd21a7e80-cbb4-492a-9625-d8ea8f844515', courseName: '科创劳动教育的实践路径', resourceTotalCount: 18 },
                    { courseId: 'e6a702f8-552d-49f6-89e7-b40ce5e445af', courseName: '特教教师课堂教学专题培训', resourceTotalCount: 220 }
                ];
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', function () {
            log('页面加载完成，正在初始化课程配置...');
            // 立即显示预设课程，避免等待网络请求
            const trainType = getSelectedTrainType();
            const presetCourses = getPresetCourses(trainType);
            generateCourseConfigUI(presetCourses);
            log(`已显示${presetCourses.length}个课程的配置界面`);
        });

        // 日志输出函数
        function log(message) {
            const logOutput = document.getElementById('logOutput');
            logOutput.innerHTML += message + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            const logOutput = document.getElementById('logOutput');
            logOutput.innerHTML = '';
        }

        // 清空JSONP输入
        function clearJsonpInput() {
            const jsonpInput = document.getElementById('jsonpInput');
            jsonpInput.value = '';
        }

        // 随机字符串生成
        function randomStr(length) {
            const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += characters.charAt(Math.floor(Math.random() * characters.length));
            }
            return result;
        }

        // 生成UUID的兼容函数
        function generateUUID(withDash = true) {
            let d = new Date().getTime();
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                let r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
            });
            return withDash ? uuid : uuid.replace(/-/g, '');
        }

        // 生成nonce
        function nonce(diff) {
            return `${Date.now() + diff}:${randomStr(8)}`;
        }

        // HMAC-SHA256签名
        async function getSign(url, nonce, methodType, macKey) {
            const uri = new URL(url);
            const relativePath = uri.pathname + (uri.search || '');
            const authority = uri.host;
            const message = `${nonce}\n${methodType.toUpperCase()}\n${relativePath}\n${authority}\n`;

            const encoder = new TextEncoder();
            const key = await crypto.subtle.importKey(
                'raw',
                encoder.encode(macKey),
                { name: 'HMAC', hash: 'SHA-256' },
                false,
                ['sign']
            );

            const signature = await crypto.subtle.sign(
                'HMAC',
                key,
                encoder.encode(message)
            );

            return btoa(String.fromCharCode(...new Uint8Array(signature)));
        }

        // 获取认证Cookie
        async function getCookie(methodType, url) {
            const diff = -Math.floor(Math.random() * (600 - 500) + 500);
            const nonceStr = nonce(diff);
            const mac = await getSign(url, nonceStr, methodType, macKey);
            return `MAC id="${macId}",nonce="${nonceStr}",mac="${mac}"`;
        }

        // 加载JSONP
        function loadJSONP(url) {
            return new Promise((resolve, reject) => {
                const callbackName = 'jsonpCallback_' + Math.random().toString(36).substr(2, 9);
                window[callbackName] = function (data) {
                    document.head.removeChild(script);
                    delete window[callbackName];
                    resolve(data);
                };

                const script = document.createElement('script');
                script.src = url + (url.includes('?') ? '&' : '?') + 'callback=' + callbackName;
                script.onerror = () => {
                    document.head.removeChild(script);
                    delete window[callbackName];
                    reject(new Error('JSONP request failed'));
                };
                document.head.appendChild(script);
            });
        }

        // HTTP请求封装
        async function request(url, method = 'GET', body = null, extraHeaders = {}, enableTiming = false) {
            const startTime = Date.now();

            const cookie = await getCookie(method, url);
            const headers = {
                'Authorization': cookie,
                'sdp-app-id': appId,
                'Content-Type': 'application/json',
                ...extraHeaders
            };

            const options = {
                method,
                headers,
                mode: 'cors',
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            try {
                const response = await fetch(url, options);
                const endTime = Date.now();
                const duration = endTime - startTime;

                // 如果启用耗时统计，输出日志
                if (enableTiming) {
                    const urlPath = new URL(url).pathname;
                    const durationInSeconds = (duration / 1000).toFixed(2);
                    log(`[请求耗时] ${method} ${urlPath} - ${durationInSeconds}秒 (状态: ${response.status})`);
                }

                // 记录响应状态
                // log(`请求 ${url} 状态: ${response.status}`);

                // 如果响应为空，直接返回null
                const text = await response.text();
                if (!text) {
                    return null;
                }

                // 尝试解析JSON
                try {
                    return JSON.parse(text);
                } catch (e) {
                    // 如果不是JSON格式，直接返回文本
                    return text;
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (enableTiming) {
                    const urlPath = new URL(url).pathname;
                    const durationInSeconds = (duration / 1000).toFixed(2);
                    log(`[请求耗时] ${method} ${urlPath} - ${durationInSeconds}秒 (失败: ${error.message})`);
                }

                log(`请求错误: ${error.message}`);
                throw error;
            }
        }

        // 获取培训课程列表
        async function trainCourses() {
            const trainType = getSelectedTrainType();
            const currentTrainId = getCurrentTrainId();
            const url = trainType === 'student'
                ? `${API_BASE_URLS.TRAIN_API}${currentTrainId}/train_courses.json`
                : `${API_BASE_URLS.TRAIN_API}2025sqpx/train_courses.json`;
            try {
                const response = await request(url);
                if (!response) {
                    throw new Error('获取课程列表失败');
                }

                // 确保response是一个数组
                const data = Array.isArray(response) ? response : response.data || [];

                // 详细的课程列表日志输出
                log('获取到的课程列表:');
                data.forEach((course, index) => {
                    log(`${index + 1}. 课程名称: ${course.title || '未知'}`);
                    log(`   课程ID: ${course.course_id || '未知'}`);
                    log(`   资源总数: ${course.resource_total_count || 0}`);
                });

                // 处理课程数据，确保字段名称与Java版本一致
                const processedData = data.map(course => ({
                    courseId: course.course_id,
                    courseName: course.title,
                    resourceTotalCount: parseInt(course.resource_total_count) || 0,
                    frontCoverUrl: course.front_cover_url
                })).filter(course => course.courseId); // 只保留有courseId的课程

                if (processedData.length === 0) {
                    log('警告: 没有找到有效的课程');
                } else {
                    // 生成课程配置UI
                    generateCourseConfigUI(processedData);
                }

                return processedData;

            } catch (error) {
                log(`获取课程列表错误: ${error.message}`);
                throw error;
            }
        }

        // 生成课程配置UI
        function generateCourseConfigUI(courseList) {
            const container = document.getElementById('courseConfigContainer');

            if (!container) {
                log('错误: 无法找到课程配置容器');
                return;
            }

            // 清空现有内容
            container.innerHTML = '';

            // 为每个课程创建配置项
            courseList.forEach((course, index) => {
                // 只有在课程配置不存在时才初始化为null，保留已有配置
                if (!CourseConfigManager.configs.has(course.courseId)) {
                    CourseConfigManager.configs.set(course.courseId, null);
                }

                // 获取当前配置值
                const currentConfig = CourseConfigManager.configs.get(course.courseId);
                const inputValue = currentConfig !== null ? currentConfig : '';

                const configItem = document.createElement('div');
                configItem.className = 'course-config-item';
                configItem.innerHTML = `
                    <div class="course-config-header">${course.courseName}</div>
                    <div class="course-config-info">课程ID: ${course.courseId} | 总资源数: ${course.resourceTotalCount}</div>
                    <input type="number"
                           class="course-config-input"
                           data-course-id="${course.courseId}"
                           placeholder="留空学习全部资源"
                           min="1"
                           max="${course.resourceTotalCount}"
                           value="${inputValue}"
                           onchange="updateCourseConfig('${course.courseId}', this.value)">
                `;
                container.appendChild(configItem);
            });

            log(`已生成${courseList.length}个课程的配置界面`);
        }

        // 获取课程资源
        async function getResourceByCourseId(courseId) {
            const url = `${API_BASE_URLS.COURSE_API}business_courses/${courseId}/course_relative_infos/zh-CN.json`;
            const data = await request(url);
            const activitySetId = data.course_detail.activity_set_id;
            return await fullJson(activitySetId, courseId);
        }

        // 获取完整课程JSON
        async function fullJson(activitySetId, courseId) {
            const url = `${API_BASE_URLS.COURSE_API}activity_sets/${activitySetId}/fulls.json`;
            const data = await request(url);
            const courses = [];
            traverseNodes(data, data.nodes, courses);
            courses.forEach(course => course.courseId = courseId);
            return courses;
        }

        // 遍历节点
        function traverseNodes(respJson, nodes, courses) {
            for (const node of nodes) {
                if (!node.child_nodes || node.child_nodes.length === 0) {
                    const relations = node.relations;
                    if (relations && relations.activity && relations.activity.activity_resources) {
                        const resources = relations.activity.activity_resources;
                        if (resources.length > 0) {
                            const resource = resources[0];

                            // 添加空值检查和资源有效性验证
                            if (resource && resource.resource_id && resource.study_time && resource.study_time > 0) {
                                // 安全地获取title，只处理有标题的资源
                                let title = null;

                                // 尝试多种方式获取标题
                                if (resource.video_extend && resource.video_extend.title) {
                                    title = resource.video_extend.title;
                                } else if (resource.title) {
                                    title = resource.title;
                                } else if (resource.name) {
                                    title = resource.name;
                                } else if (resource.display_name) {
                                    title = resource.display_name;
                                } else if (resource.resource_name) {
                                    title = resource.resource_name;
                                } else if (resource.video_extend && resource.video_extend.name) {
                                    title = resource.video_extend.name;
                                }

                                // 只处理有标题的资源，跳过无标题的资源
                                if (title && title.trim()) {

                                    courses.push({
                                        courseName: respJson.activity_set_name,
                                        title: title,
                                        activityId: resource.activity_id,
                                        resourceId: resource.resource_id,
                                        studyTime: resource.study_time || 0
                                    });
                                } else {
                                    // 跳过无标题的资源（不记录日志，因为这是正常的筛选）
                                    console.debug('跳过无标题资源:', resource.resource_id);
                                }
                            } else {
                                // 跳过无效资源（无study_time或study_time为0的资源）
                                console.debug('跳过无效资源:', resource ? resource.resource_id : 'null resource');
                            }
                        }
                    }
                } else {
                    traverseNodes(respJson, node.child_nodes, courses);
                }
            }
        }

        // 学习单个资源
        async function learningOneResource(courses, resourceId, courseId, cover) {
            // 在函数开始时检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            const course = courses.find(c => c.resourceId === resourceId);
            if (!course) return;

            await learningByResourceId(course.resourceId, course.studyTime + 1);

            // 在API调用后检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            await getPosition(course.resourceId);

            // 再次检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            log(`完成学习: ${course.courseName} - ${course.title}`);

            let studyDetail = await getStudyDetail(courseId);
            let detailJson;

            // 学过的课程，这里每次都新学
            if (studyDetail && studyDetail !== 'null') {
                try {
                    detailJson = JSON.parse(studyDetail);
                    delete detailJson.id;
                    delete detailJson.client_id;
                    delete detailJson.create_time;
                    delete detailJson.update_time;

                    let ext_info = {};
                    try {
                        ext_info = JSON.parse(detailJson.ext_info || '{}');
                    } catch (e) {
                        ext_info = {};
                    }

                    // 重写last_learning_activity
                    ext_info.last_learning_activity = {
                        activity_id: course.activityId,
                        title: course.title
                    };

                    // 更新activity_last_learning_resource
                    if (!ext_info.activity_last_learning_resource) {
                        ext_info.activity_last_learning_resource = {};
                    }
                    ext_info.activity_last_learning_resource[course.activityId] = course.resourceId;

                    // 更新activity_progress
                    if (!ext_info.activity_progress) {
                        ext_info.activity_progress = {};
                    }
                    ext_info.activity_progress[course.activityId] = 2;

                    // 更新resource_progress
                    if (!ext_info.resource_progress) {
                        ext_info.resource_progress = {};
                    }
                    ext_info.resource_progress[course.resourceId] = 2;

                    // 更新resource_max_pos
                    if (!ext_info.resource_max_pos) {
                        ext_info.resource_max_pos = {};
                    }
                    ext_info.resource_max_pos[course.resourceId] = {
                        pos: course.studyTime + 1,
                        type: "video"
                    };

                    // 确保其他必要字段存在
                    ext_info.cv = ext_info.cv || 1;
                    ext_info.platform = ext_info.platform || 'web';
                    ext_info.tags = ext_info.tags || tags;
                    ext_info.origin = ext_info.origin || origin;
                    ext_info.cover = ext_info.cover || cover;
                    ext_info.miniwork_progress = ext_info.miniwork_progress || {};
                    ext_info.activity_exam_progress = ext_info.activity_exam_progress || {};
                    ext_info.activity_event = ext_info.activity_event || {};
                    ext_info.resource_study_time_ignore = ext_info.resource_study_time_ignore || [];
                    ext_info.additional_params = ext_info.additional_params || {
                        library_id: 'bb042e69-9a11-49a1-af22-0c3fab2e92b9'
                    };

                    detailJson.ext_info = JSON.stringify(ext_info);
                    detailJson.progress = (detailJson.progress || 0) + 3;

                } catch (error) {
                    log(`处理已有学习记录时出错: ${error.message}`);
                    detailJson = createNewStudyDetail(course, cover);
                }
            } else {
                detailJson = createNewStudyDetail(course, cover);
            }

            // 在最后的API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            await postStudyDetail(detailJson, courseId);

            // 在最后一个API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            await async(JSON.stringify(detailJson), courseId);
        }

        // 创建新的学习详情
        function createNewStudyDetail(course, cover) {
            return {
                user_id: userId,
                resource_id: course.courseId,
                resource_name: course.courseName,
                resource_type: 't_course',
                catalog_type: 'teacherTraining',
                topic_type: course.courseId,
                progress: 1,
                status: 1,
                ext_info: JSON.stringify({
                    cv: 1,
                    platform: 'web',
                    tags,
                    origin,
                    cover,
                    last_learning_activity: {
                        activity_id: course.activityId,
                        title: course.title
                    },
                    activity_last_learning_resource: {
                        [course.activityId]: course.resourceId
                    },
                    activity_progress: {
                        [course.activityId]: 2
                    },
                    resource_progress: {
                        [course.resourceId]: 2
                    },
                    resource_max_pos: {
                        [course.resourceId]: {
                            pos: course.studyTime + 1,
                            type: 'video'
                        }
                    },
                    miniwork_progress: {},
                    activity_exam_progress: {},
                    activity_event: {},
                    resource_study_time_ignore: [],
                    additional_params: {
                        library_id: 'bb042e69-9a11-49a1-af22-0c3fab2e92b9'
                    }
                })
            };
        }

        // 更新学习位置
        async function learningByResourceId(resourceId, position) {
            // 在API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return null;
            }

            const url = `${API_BASE_URLS.STUDY_RECORD_API}resource_learning_positions/${resourceId}/${userId}`;
            await request(url, 'PUT', { position }, {}, true);

            // 在第二个API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return null;
            }

            return await getPosition(resourceId);
        }

        // 获取学习位置
        async function getPosition(resourceId) {
            const url = `${API_BASE_URLS.STUDY_RECORD_API}resource_learning_positions/${resourceId}/${userId}`;
            return await request(url, 'GET', null, {}, true);
        }

        // 获取学习详情
        async function getStudyDetail(courseId) {
            const url = `${API_BASE_URLS.STUDY_RECORD_API}study_details/${courseId}/${userId}`;
            try {
                const result = await request(url);
                return JSON.stringify(result);
            } catch (error) {
                return null;
            }
        }

        // 提交学习详情
        async function postStudyDetail(detail, courseId) {
            const url = `${API_BASE_URLS.STUDY_RECORD_API}study_details`;
            return await request(url, 'POST', detail, {}, true);
        }

        // 同步进度
        async function async(body, courseId) {
            const url = `${API_BASE_URLS.TRAIN_GATEWAY_API}spi/trains/${getCurrentTrainId()}/courses/${courseId}/progress/actions/async`;
            const headers = {
                'Host': 'elearning-train-gateway.ykt.eduyun.cn',
                'device_id': (crypto.randomUUID?.() || generateUUID()).toLowerCase(),
                'sdp-xpath-id': (crypto.randomUUID?.() || generateUUID()).toLowerCase()
            };
            return await request(url, 'POST', JSON.parse(body), headers, true);
        }

        // 加入培训
        async function join() {
            const currentTrainId = getCurrentTrainId();
            const url = `${API_BASE_URLS.TRAIN_GATEWAY_V2_API}spi/trains/${currentTrainId}/actions/async_joins`;
            const cookie = await getCookie('post', url);
            const headers = {
                'Authorization': cookie + '1',
                'sdp-app-id': appId,
                'Host': 'elearning-train-gateway.ykt.eduyun.cn'
            };
            const response = await request(url, 'POST', null, headers);
            log('已加入培训');
            return response;
        }

        // 主函数
        async function learningByCourseAndCount() {
            try {
                await join();
                const courseList = await trainCourses();

                if (!courseList || courseList.length === 0) {
                    log('错误: 未获取到任何课程');
                    return;
                }

                log(`总共获取到${courseList.length}个课程，开始处理...`);

                for (const course of courseList) {
                    // 检查是否需要停止学习
                    if (shouldStop) {
                        log('学习已停止');
                        return;
                    }

                    try {
                        log(`\n开始学习: ${course.courseName}`);
                        log(`课程ID: ${course.courseId}`);
                        log(`资源总数: ${course.resourceTotalCount}`);

                        const resourceTotalCount = course.resourceTotalCount || 0;
                        log(`课程元数据显示资源总数: ${resourceTotalCount}`);

                        const courses = await getResourceByCourseId(course.courseId);

                        if (!courses || courses.length === 0) {
                            log(`警告: ${course.courseName} 未找到可学习的资源`);
                            continue;
                        }

                        log(`实际找到 ${courses.length} 个可学习的资源`);

                        // 获取该课程的学习数量配置
                        const courseResourceCount = getCourseResourceCount(course.courseId);
                        let num;
                        let learningStrategy;

                        if (courseResourceCount !== null) {
                            // 用户为该课程指定了数量
                            num = Math.min(courseResourceCount, courses.length);
                            learningStrategy = `课程配置 (${courseResourceCount}个)`;
                            if (courseResourceCount > courses.length) {
                                log(`注意: 课程配置学习${courseResourceCount}个资源，但实际只有${courses.length}个资源，将学习全部${num}个资源`);
                            }
                        } else {
                            // 用户未为该课程设置，学习所有实际资源
                            num = courses.length;
                            learningStrategy = '默认策略 (学习全部)';
                        }

                        log(`学习策略: ${learningStrategy}`);
                        log(`计划学习资源数: ${num}/${courses.length}`);

                        for (let i = 0; i < num && i < courses.length; i++) {
                            // 检查是否需要停止学习
                            if (shouldStop) {
                                log('学习已停止');
                                return;
                            }

                            log(`正在学习第 ${i + 1}/${num} 个资源: ${courses[i].title || '未知资源'}`);

                            // 在开始学习资源前再次检查停止状态
                            if (shouldStop) {
                                log('学习已停止');
                                return;
                            }

                            await learningOneResource(courses, courses[i].resourceId, course.courseId, course.frontCoverUrl);

                            // 在完成资源学习后检查停止状态
                            if (shouldStop) {
                                log('学习已停止');
                                return;
                            }
                        }

                        const studyDetail = await getStudyDetail(course.courseId);
                        if (studyDetail) {
                            const studyDetailJson = JSON.parse(studyDetail);
                            delete studyDetailJson.id;
                            delete studyDetailJson.client_id;
                            delete studyDetailJson.create_time;
                            delete studyDetailJson.update_time;
                            await async(JSON.stringify(studyDetailJson), course.courseId);
                        }
                        log(`完成课程: ${course.courseName}\n`);
                    } catch (error) {
                        log(`处理课程 ${course.courseName} 时发生错误: ${error.message}`);
                        // 继续处理下一个课程
                        continue;
                    }
                }
                log('所有课程处理完成！');
            } catch (error) {
                log(`错误: ${error.message}`);
                resetLearningState();
            }
        }

        // 切换学习状态的主控制函数
        async function toggleLearning() {
            if (isLearning) {
                stopLearning();
            } else {
                await startLearning();
            }
        }

        // 停止学习函数
        function stopLearning() {
            log('正在停止学习...');
            shouldStop = true;
            isLearning = false;
            updateButtonState();
            log('用户手动停止学习');

            // 确保状态被正确设置
            setTimeout(() => {
                if (shouldStop && !isLearning) {
                    log('学习已完全停止');
                }
            }, 100);
        }

        // 更新按钮状态函数
        function updateButtonState() {
            const button = document.getElementById('learningButton');
            if (isLearning) {
                button.textContent = '停止学习';
                button.className = 'stop-button';
            } else {
                button.textContent = '开始学习';
                button.className = '';
            }
        }

        // 重置学习状态函数
        function resetLearningState() {
            isLearning = false;
            shouldStop = false;
            updateButtonState();
        }

        // 启动函数
        async function startLearning() {
            // 设置学习状态
            isLearning = true;
            shouldStop = false;
            updateButtonState();

            // 清空日志
            clearLog();

            const jsonpInput = document.getElementById('jsonpInput').value;
            if (!jsonpInput) {
                log('请输入JSONP响应数据');
                resetLearningState();
                return;
            }

            try {
                // 清理输入数据
                const cleanInput = jsonpInput.replace(/\s+/g, '');
                // 修改正则表达式以适应更复杂的函数名
                const match = cleanInput.match(/^[\w_]+\((.*)\)$/);

                if (!match) {
                    log('JSONP格式不正确');
                    resetLearningState();
                    return;
                }

                const jsonStr = match[1];
                const data = JSON.parse(jsonStr);

                if (!data.$body || !data.$body.user_id || !data.$body.mac_key || !data.$body.access_token) {
                    log('JSONP数据格式不正确，缺少必要的字段');
                    resetLearningState();
                    return;
                }

                userId = data.$body.user_id;
                macKey = data.$body.mac_key;
                macId = data.$body.access_token;

                log(`用户ID: ${userId}`);
                log('认证信息获取成功');

                // 显示学习配置信息
                log('课程学习配置:');
                let hasCustomConfig = false;
                for (const [courseId, count] of CourseConfigManager.configs) {
                    if (count !== null) {
                        log(`  课程 ${courseId}: ${count} 个资源`);
                        hasCustomConfig = true;
                    }
                }
                if (!hasCustomConfig) {
                    log('  所有课程均使用默认策略 (学习全部资源)');
                }

                await learningByCourseAndCount();

                // 学习完成，重置状态
                if (!shouldStop) {
                    log('所有课程学习完成！');
                }
                resetLearningState();
            } catch (error) {
                log(`错误: ${error.message}`);
                log('请检查输入的JSONP数据格式是否正确');
                resetLearningState();
            }
        }
    </script>
</body>

</html>